package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.exceptions.AppRTException;
import com.skillsync.applyr.core.models.entities.Qualification;
import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.modules.company.models.QualificationRequestDTO;
import com.skillsync.applyr.modules.company.models.QualificationUpdateDTO;
import com.skillsync.applyr.modules.company.models.QualificationBulkImportDTO;
import com.skillsync.applyr.modules.company.models.BulkImportResultDTO;
import com.skillsync.applyr.modules.company.repositories.QualificationRepository;
import com.skillsync.applyr.modules.company.repositories.ApplicationRepository;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Service
public class QualificationService {

    private final QualificationRepository qualificationRepository;
    private final ApplicationRepository applicationRepository;

    public QualificationService(QualificationRepository qualificationRepository, ApplicationRepository applicationRepository) {
        this.qualificationRepository = qualificationRepository;
        this.applicationRepository = applicationRepository;
    }

    public SuccessResponse addQualification(QualificationRequestDTO qualification) {
        try {
            Qualification raw = new Qualification(qualification);
            qualificationRepository.save(raw);
            return new SuccessResponse("Qualification added successfully");
        } catch (Exception e) {
            throw new AppRTException("Unable to add qualification.", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public SuccessResponse updateQualification(QualificationUpdateDTO qualificationUpdateDTO) {
        try {
            Qualification qualification = qualificationRepository.findByQualificationId(qualificationUpdateDTO.getQualificationId());
            if (qualification == null) {
                throw new AppRTException("Qualification not found with ID: " + qualificationUpdateDTO.getQualificationId(), HttpStatus.NOT_FOUND);
            }

            // Update qualification properties
            qualification.setQualificationName(qualificationUpdateDTO.getQualificationName());
            qualification.setRplPrice(qualificationUpdateDTO.getRplPrice());
            qualification.setRtoPriceHigh(qualificationUpdateDTO.getRtoPriceHigh());
            qualification.setEnrollmentPrice(qualificationUpdateDTO.getEnrollmentPrice());
            qualification.setOffshorePrice(qualificationUpdateDTO.getOffshorePrice());
            qualification.setNotes(qualificationUpdateDTO.getNotes());
            qualification.setType(qualificationUpdateDTO.getType());
            qualification.setProcessingTime(qualificationUpdateDTO.getProcessingTime());
            qualification.setDemand(qualificationUpdateDTO.getDemand());
            qualification.setChecklist(qualificationUpdateDTO.getChecklist());

            qualificationRepository.save(qualification);
            return new SuccessResponse("Qualification updated successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Unable to update qualification: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public SuccessResponse deleteQualification(String qualificationId) {
        try {
            Qualification qualification = qualificationRepository.findByQualificationId(qualificationId);
            if (qualification == null) {
                throw new AppRTException("Qualification not found with ID: " + qualificationId, HttpStatus.NOT_FOUND);
            }

            // Check if qualification is being used in any applications
            // We need to check if any SoldQualifications reference this qualification
            // For now, we'll allow deletion and let the database handle constraints
            // In a production system, you might want to check for references first

            qualificationRepository.delete(qualification);
            return new SuccessResponse("Qualification deleted successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Unable to delete qualification: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public BulkImportResultDTO bulkImportQualifications(MultipartFile file) {
        List<String> errors = new ArrayList<>();
        int totalProcessed = 0;
        int successCount = 0;
        int updateCount = 0;
        int createCount = 0;
        int errorCount = 0;

        try {
            if (file.isEmpty()) {
                throw new AppRTException("File is empty", HttpStatus.BAD_REQUEST);
            }

            if (!file.getOriginalFilename().endsWith(".xlsx")) {
                throw new AppRTException("Only .xlsx files are supported", HttpStatus.BAD_REQUEST);
            }

            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            // Skip header row (row 0)
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                totalProcessed++;
                try {
                    QualificationBulkImportDTO importData = parseRowToDTO(row);

                    // Check if qualification exists
                    Qualification existingQualification = qualificationRepository.findByQualificationId(importData.getCode());

                    if (existingQualification != null) {
                        // Update existing qualification
                        updateQualificationFromImport(existingQualification, importData);
                        qualificationRepository.save(existingQualification);
                        updateCount++;
                    } else {
                        // Create new qualification
                        Qualification newQualification = createQualificationFromImport(importData);
                        qualificationRepository.save(newQualification);
                        createCount++;
                    }
                    successCount++;
                } catch (Exception e) {
                    errorCount++;
                    errors.add("Row " + (i + 1) + ": " + e.getMessage());
                }
            }

            workbook.close();
        } catch (IOException e) {
            throw new AppRTException("Error reading Excel file: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Error processing bulk import: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return new BulkImportResultDTO(totalProcessed, successCount, updateCount, createCount, errorCount, errors);
    }

    private QualificationBulkImportDTO parseRowToDTO(Row row) {
        QualificationBulkImportDTO dto = new QualificationBulkImportDTO();

        // Expected columns: Category, Code, Qualification Name, RPL Low, RPL High, ENROLLMENT, Offshore Low, Notes, Processing Time, Demand
        dto.setCategory(getCellValueAsString(row.getCell(0)));
        dto.setCode(getCellValueAsString(row.getCell(1)));
        dto.setQualificationName(getCellValueAsString(row.getCell(2)));
        dto.setRplLow(getCellValueAsDouble(row.getCell(3)));
        dto.setRplHigh(getCellValueAsDouble(row.getCell(4)));
        dto.setEnrollment(getCellValueAsDouble(row.getCell(5)));
        dto.setOffshoreLow(getCellValueAsDouble(row.getCell(6)));
        dto.setNotes(getCellValueAsString(row.getCell(7)));
        dto.setProcessingTime(getCellValueAsString(row.getCell(8)));
        dto.setDemand(getCellValueAsString(row.getCell(9)));

        // Validate required fields
        if (dto.getCode() == null || dto.getCode().trim().isEmpty()) {
            throw new RuntimeException("Qualification code is required");
        }

        return dto;
    }

    private String getCellValueAsString(Cell cell) {
        if (cell == null) return "";

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return "";
        }
    }

    private double getCellValueAsDouble(Cell cell) {
        if (cell == null) return 0.0;

        switch (cell.getCellType()) {
            case NUMERIC:
                return cell.getNumericCellValue();
            case STRING:
                try {
                    return Double.parseDouble(cell.getStringCellValue().trim());
                } catch (NumberFormatException e) {
                    return 0.0;
                }
            default:
                return 0.0;
        }
    }

    private Qualification createQualificationFromImport(QualificationBulkImportDTO importData) {
        Qualification qualification = new Qualification();
        qualification.setQualificationId(importData.getCode());
        qualification.setQualificationName(importData.getQualificationName());
        qualification.setRplPrice(importData.getRplLow());
        qualification.setRtoPriceHigh(importData.getRplHigh());
        qualification.setEnrollmentPrice(importData.getEnrollment());
        qualification.setOffshorePrice(importData.getOffshoreLow());
        qualification.setNotes(importData.getNotes());
        qualification.setType(importData.getCategory());
        qualification.setProcessingTime(importData.getProcessingTime());
        qualification.setDemand(importData.getDemand());
        qualification.setChecklist(new ArrayList<>()); // Initialize empty checklist
        qualification.setTotalApplications(0);
        return qualification;
    }

    private void updateQualificationFromImport(Qualification qualification, QualificationBulkImportDTO importData) {
        qualification.setQualificationName(importData.getQualificationName());
        qualification.setRplPrice(importData.getRplLow());
        qualification.setRtoPriceHigh(importData.getRplHigh());
        qualification.setEnrollmentPrice(importData.getEnrollment());
        qualification.setOffshorePrice(importData.getOffshoreLow());
        qualification.setNotes(importData.getNotes());
        qualification.setType(importData.getCategory());
        qualification.setProcessingTime(importData.getProcessingTime());
        qualification.setDemand(importData.getDemand());
        // Keep existing checklist and totalApplications
    }
}
