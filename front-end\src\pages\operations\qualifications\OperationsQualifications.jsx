import React, { useState, useMemo } from "react";
import { useGetQualificationsQuery } from "../../../services/SalesAPIService";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfoCircle } from "@fortawesome/free-solid-svg-icons";

const OperationsQualifications = () => {
  // State for filtering and sorting
  const [filter, setFilter] = useState("");
  const [sortOrder, setSortOrder] = useState("asc");
  const [selectedType, setSelectedType] = useState("All"); // State for type filter

  // Drawer states
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedQualification, setSelectedQualification] = useState(null);

  // Fetch qualifications using the SalesAPIService hook
  const {
    data: qualifications = [],
    error,
    isLoading,
    isFetching,
    refetch,
  } = useGetQualificationsQuery();

  // Extract unique types from qualifications data
  const uniqueTypes = useMemo(() => {
    const types = qualifications.map((q) => q.type);
    return ["All", ...Array.from(new Set(types))];
  }, [qualifications]);

  // Handle sort order toggle
  const handleSortChange = () => {
    setSortOrder((prevOrder) => (prevOrder === "asc" ? "desc" : "asc"));
  };

  // Handle Information button click
  const handleInfoClick = (qualification) => {
    setSelectedQualification(qualification);
    setIsDrawerOpen(true);
  };

  // Handle drawer close
  const handleDrawerClose = () => {
    setIsDrawerOpen(false);
    setSelectedQualification(null);
  };

  // Filter and sort logic for qualifications
  const filteredQualifications = useMemo(() => {
    return qualifications
      .filter((qualification) => {
        // Filter by search input
        if (filter) {
          const lowerFilter = filter.toLowerCase();
          const matchesSearch =
            qualification.qualificationId.toLowerCase().includes(lowerFilter) ||
            qualification.qualificationName.toLowerCase().includes(lowerFilter);
          if (!matchesSearch) return false;
        }

        // Filter by type
        if (selectedType !== "All") {
          return qualification.type === selectedType;
        }

        return true;
      })
      .sort((a, b) => {
        if (sortOrder === "asc") {
          return a.qualificationName.localeCompare(b.qualificationName);
        } else {
          return b.qualificationName.localeCompare(a.qualificationName);
        }
      });
  }, [qualifications, filter, sortOrder, selectedType]);

  // Helper function to display price or N/A
  const displayPrice = (price) =>
    price === 0 ? "N/A" : `$${price.toFixed(2)}`;

  return (
    <div className="w-full py-6">
      {/* Qualifications Section */}
      <div>
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Qualifications</h1>
            <p className="mt-1 text-sm text-gray-500">
              View qualification catalog and pricing
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex items-center space-x-3">
            <button
              onClick={() => refetch()}
              className="flex items-center text-gray-700 bg-white border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-50 p-4 mb-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0 md:space-x-4">
            <div className="w-full md:w-1/2 relative">
              <input
                type="text"
                placeholder="Search by ID or Name"
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              />
              <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <div className="flex items-center space-x-3 w-full md:w-auto">
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="border border-gray-200 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              >
                {uniqueTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
              <button
                onClick={handleSortChange}
                className="flex items-center bg-white border border-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                </svg>
                {sortOrder === "asc" ? "A-Z" : "Z-A"}
              </button>
            </div>
          </div>
        </div>

        {/* Handle Loading State */}
        {isLoading || isFetching ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#6E39CB]"></div>
            <span className="ml-3 text-gray-500">Loading qualifications...</span>
          </div>
        ) : error ? (
          // Handle Error State
          <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">
                  Error fetching qualifications. Please try again later.
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border border-gray-50 overflow-x-auto">
            <div className="p-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-gray-900">Qualification List</h2>
            </div>
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-[#F4F5F9]">
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ID
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    RPL Low
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    RPL High
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Enrollment
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Offshore
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Processing Time
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Demand
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-100">
                {filteredQualifications.length === 0 ? (
                  <tr>
                    <td
                      colSpan={10}
                      className="px-6 py-8 whitespace-nowrap text-sm text-gray-500 text-center"
                    >
                      <div className="flex flex-col items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <p>{filter || selectedType !== "All" ? "No qualifications match your search." : "There are no available Qualifications."}</p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  filteredQualifications.map((qualification) => (
                    <tr key={qualification.id} className="hover:bg-[#F4F5F9] transition-colors">
                      <td className="px-4 py-4 whitespace-nowrap">
                        <span className="text-sm font-medium text-gray-900 bg-gray-100 px-2.5 py-0.5 rounded-full">
                          {qualification.qualificationId}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 max-w-xs truncate">
                        {qualification.qualificationName}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                          {qualification.type || "N/A"}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                        {displayPrice(qualification.rplPrice)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                        {displayPrice(qualification.rtoPriceHigh || 0)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                        {displayPrice(qualification.enrollmentPrice)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                        {displayPrice(qualification.offshorePrice)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                        {qualification.processingTime || "N/A"}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          qualification.demand === 'High' ? 'bg-red-100 text-red-800' :
                          qualification.demand === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          qualification.demand === 'Low' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {qualification.demand || "N/A"}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-center">
                        <button
                          onClick={() => handleInfoClick(qualification)}
                          className="text-[#6E39CB] hover:text-[#5E2CB8] transition-colors p-1"
                          title="View Details"
                        >
                          <FontAwesomeIcon icon={faInfoCircle} className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Right Drawer for Qualification Notes */}
      {isDrawerOpen && selectedQualification && (
        <div className="fixed inset-0 z-50 flex">
          {/* Overlay */}
          <div
            className="fixed inset-0 bg-black opacity-50"
            onClick={handleDrawerClose}
          ></div>
          {/* Drawer */}
          <div className="relative ml-auto w-full max-w-md bg-white h-full shadow-xl overflow-y-auto">
            <div className="sticky top-0 bg-white z-10 border-b border-gray-100 p-6">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-xl font-bold text-gray-900">
                  Qualification Details
                </h2>
                <button
                  onClick={handleDrawerClose}
                  className="text-gray-400 hover:text-gray-500 focus:outline-none"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
              <p className="text-sm text-gray-500">
                View detailed information about this qualification
              </p>
            </div>

            <div className="p-6">
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {selectedQualification.qualificationName}
                </h3>
                <div className="bg-[#F4F5F9] rounded-lg p-4 mb-6">
                  <div className="flex items-center">
                    <div className="bg-[#6E39CB] bg-opacity-10 p-2 rounded-full mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Qualification ID</p>
                      <p className="text-base font-semibold text-gray-900">{selectedQualification.qualificationId}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="bg-white rounded-lg border border-gray-100 p-4">
                  <p className="text-sm font-medium text-gray-500 mb-1">Type</p>
                  <p className="text-base font-medium text-gray-900">{selectedQualification.type || "N/A"}</p>
                </div>

                <div className="bg-white rounded-lg border border-gray-100 p-4">
                  <p className="text-sm font-medium text-gray-500 mb-1">Processing Time</p>
                  <p className="text-base font-medium text-gray-900">{selectedQualification.processingTime || "N/A"}</p>
                </div>

                <div className="bg-white rounded-lg border border-gray-100 p-4">
                  <p className="text-sm font-medium text-gray-500 mb-1">Demand</p>
                  <p className="text-base font-medium text-gray-900">{selectedQualification.demand || "N/A"}</p>
                </div>

                <div className="bg-white rounded-lg border border-gray-100 p-4">
                  <p className="text-sm font-medium text-gray-500 mb-1">RPL Low Price</p>
                  <p className="text-base font-medium text-gray-900">
                    {displayPrice(selectedQualification.rplPrice)}
                  </p>
                </div>

                <div className="bg-white rounded-lg border border-gray-100 p-4">
                  <p className="text-sm font-medium text-gray-500 mb-1">RPL High Price</p>
                  <p className="text-base font-medium text-gray-900">
                    {displayPrice(selectedQualification.rtoPriceHigh || 0)}
                  </p>
                </div>

                <div className="bg-white rounded-lg border border-gray-100 p-4">
                  <p className="text-sm font-medium text-gray-500 mb-1">
                    Enrollment Price
                  </p>
                  <p className="text-base font-medium text-gray-900">
                    {displayPrice(selectedQualification.enrollmentPrice)}
                  </p>
                </div>

                <div className="bg-white rounded-lg border border-gray-100 p-4">
                  <p className="text-sm font-medium text-gray-500 mb-1">Offshore Price</p>
                  <p className="text-base font-medium text-gray-900">
                    {displayPrice(selectedQualification.offshorePrice)}
                  </p>
                </div>
              </div>

              <div className="mb-4">
                <p className="text-sm font-medium text-gray-500 mb-2">Notes</p>
                <div className="p-4 bg-[#F4F5F9] rounded-lg">
                  <p className="text-gray-700 whitespace-pre-wrap">
                    {selectedQualification.notes || "No notes available."}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OperationsQualifications;
