package com.skillsync.applyr.core.models.entities;

import com.skillsync.applyr.modules.company.models.QualificationRequestDTO;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "qualifications")
public class Qualification {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String qualificationId;

    private String qualificationName;
    private double rplPrice;

    @Column(columnDefinition = "double precision default 0")
    private double rtoPriceHigh;

    private double enrollmentPrice;
    private double offshorePrice;

    @Column(length=10485760)
    private String notes;
    private String type;

    private String processingTime;
    private String demand;

    @ElementCollection
    private List<String> checklist;

    private int totalApplications;

    public Qualification(QualificationRequestDTO qualification) {
        this.qualificationId = qualification.getQualificationId();
        this.qualificationName = qualification.getQualificationName();
        this.rplPrice = qualification.getRplPrice();
        this.rtoPriceHigh = qualification.getRtoPriceHigh();
        this.enrollmentPrice = qualification.getEnrollmentPrice();
        this.offshorePrice = qualification.getOffshorePrice();
        this.notes = qualification.getNotes();
        this.type = qualification.getType();
        this.processingTime = qualification.getProcessingTime();
        this.demand = qualification.getDemand();
        this.checklist = qualification.getChecklist();
        this.totalApplications = 0;
    }
}
