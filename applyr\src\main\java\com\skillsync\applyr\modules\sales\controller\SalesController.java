package com.skillsync.applyr.modules.sales.controller;


import com.skillsync.applyr.core.models.entities.Qualification;
import com.skillsync.applyr.core.models.enums.TroubleStatus;
import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.modules.company.models.ProfileDTO;
import com.skillsync.applyr.modules.company.services.CompanyServices;
import com.skillsync.applyr.modules.sales.models.CreateTroubleDTO;
import com.skillsync.applyr.modules.sales.models.TroubleAnswerReqDTO;
import com.skillsync.applyr.modules.sales.models.TroublesResponseDTO;
import com.skillsync.applyr.modules.sales.services.SalesServices;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/sales")
@PreAuthorize("hasRole('SALES' AND hasRole('ADMIN'))")
public class SalesController {

    private final SalesServices salesServices;
    private final CompanyServices companyServices;

    public SalesController(SalesServices salesServices, CompanyServices companyServices) {
        this.salesServices = salesServices;
        this.companyServices = companyServices;
    }

    @GetMapping("/qualifications")
    public List<Qualification> getQualifications() {
        return salesServices.getQualifications();
    }

    @PostMapping("/trouble")
    public ResponseEntity<SuccessResponse> addTrouble(@RequestBody CreateTroubleDTO troubleDTO) {
        return ResponseEntity.ok(salesServices.addTrouble(troubleDTO));
    }

    @GetMapping("/trouble/all")
    public ResponseEntity<List<TroublesResponseDTO>> getAllTrouble() {
        return ResponseEntity.ok(salesServices.getAllTroubles());
    }

    @GetMapping("/employee/all")
    public ResponseEntity<List<ProfileDTO>> getAllEmployees() {
        return ResponseEntity.ok(companyServices.getAllEmployees());
    }

    @DeleteMapping("/trouble/{id}")
    public ResponseEntity<SuccessResponse> deleteTrouble(@PathVariable long id) {
        return ResponseEntity.ok(salesServices.deleteTrouble(id));
    }

    @PutMapping("/trouble/{id}/{status}")
    public ResponseEntity<SuccessResponse> changeStatusOfTrouble(@PathVariable long id, @PathVariable String status) {
        TroubleStatus troubleStatus = TroubleStatus.valueOf(status);
        return ResponseEntity.ok(salesServices.changeStatusOfTrouble(id, troubleStatus));
    }

    @PutMapping("/trouble/comment/{id}")
    public ResponseEntity<SuccessResponse> addComments(@PathVariable long id, @RequestBody TroubleAnswerReqDTO reqDTO) {
        return ResponseEntity.ok(salesServices.addComments(id, reqDTO));
    }

    @PutMapping("/trouble/edit/{id}")
    public ResponseEntity<SuccessResponse> editTrouble(@PathVariable long id, @RequestBody CreateTroubleDTO reqDTO) {
        return ResponseEntity.ok(salesServices.editTrouble(id, reqDTO));
    }
}
