import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faChartLine,
  faFileInvoiceDollar,
  faMoneyBillWave,
  faSpinner,
  faExclamationTriangle,
  faTrophy,
  faCheckCircle,
  faArrowUp,
  faSearch,
} from "@fortawesome/free-solid-svg-icons";

// API imports
import {
  useGetAgentXeroInvoicesQuery,
  useGetAgentXeroInBankQuery,
  useGetPerformanceLeaderboardQuery,
} from "../../services/SourceOfTruthApiService";
import {
  useGetAllTargetsQuery,
  useGetAgentDashboardThisMonthQuery,
  useGetAgentDashboardThisYearQuery
} from "../../services/CompanyAPIService";

// Utils
import { getCurrentUserFromToken } from "../../utils/jwtUtils";



const AgentMonitoringTool = () => {
  // Get current agent username
  const currentUser = getCurrentUserFromToken();
  const agentUsername = currentUser?.username;

  // Enhanced state for date range selection
  const [dateRange, setDateRange] = useState("weeklyTarget");
  const [selectedWeeklyTarget, setSelectedWeeklyTarget] = useState("");
  const [selectedWeeklyTargets, setSelectedWeeklyTargets] = useState([]);
  const [customRange, setCustomRange] = useState({ from: "", to: "" });
  const [computedStartDate, setComputedStartDate] = useState("");
  const [computedEndDate, setComputedEndDate] = useState("");

  // Search states for KPI tables
  const [kpi1SearchTerm, setKpi1SearchTerm] = useState("");
  const [kpi2SearchTerm, setKpi2SearchTerm] = useState("");

  // Fetch weekly targets
  const {
    data: weeklyTargets,
    isLoading: targetsLoading,
    isError: targetsError,
  } = useGetAllTargetsQuery();

  // Auto-select current weekly target if available
  useEffect(() => {
    if (weeklyTargets && weeklyTargets.length > 0 && !selectedWeeklyTarget) {
      const now = new Date();
      // Find a target where current date falls between start and end dates
      const currentTarget = weeklyTargets.find(target => {
        const startDate = new Date(target.startDate);
        const endDate = new Date(target.endDate);
        return now >= startDate && now <= endDate;
      });

      if (currentTarget) {
        setSelectedWeeklyTarget(currentTarget.id.toString());
      } else if (weeklyTargets.length > 0) {
        // If no current target, select the last added one
        const lastTarget = weeklyTargets[weeklyTargets.length - 1];
        setSelectedWeeklyTarget(lastTarget.id.toString());
      }
    }
  }, [weeklyTargets, selectedWeeklyTarget]);

  // Helper function to get date range values
  const getDateRangeValues = (range, customRange) => {
    const now = new Date();
    switch (range) {
      case "today":
        const today = now.toISOString().split('T')[0];
        return { startDate: today + "T00:00:00", endDate: today + "T23:59:59" };
      case "custom":
        if (customRange.from && customRange.to) {
          return {
            startDate: customRange.from + "T00:00:00",
            endDate: customRange.to + "T23:59:59"
          };
        }
        return { startDate: "", endDate: "" };
      default:
        return { startDate: "", endDate: "" };
    }
  };

  // Compute start and end dates based on selected target or date range
  useEffect(() => {
    if (dateRange === "weeklyTarget" && selectedWeeklyTarget && weeklyTargets) {
      const target = weeklyTargets.find(t => t.id.toString() === selectedWeeklyTarget);
      if (target) {
        setComputedStartDate(target.startDate);
        setComputedEndDate(target.endDate);
      }
    } else if (dateRange === "multipleTargets" && selectedWeeklyTargets.length > 0 && weeklyTargets) {
      // For multiple targets, find the earliest start and latest end
      let earliestStart = null;
      let latestEnd = null;

      selectedWeeklyTargets.forEach(targetId => {
        const target = weeklyTargets.find(t => t.id.toString() === targetId);
        if (target) {
          const startDate = new Date(target.startDate);
          const endDate = new Date(target.endDate);

          if (!earliestStart || startDate < new Date(earliestStart)) {
            earliestStart = target.startDate;
          }
          if (!latestEnd || endDate > new Date(latestEnd)) {
            latestEnd = target.endDate;
          }
        }
      });

      if (earliestStart && latestEnd) {
        setComputedStartDate(earliestStart);
        setComputedEndDate(latestEnd);
      }
    } else if (dateRange === "custom") {
      const { startDate, endDate } = getDateRangeValues(dateRange, customRange);
      setComputedStartDate(startDate);
      setComputedEndDate(endDate);
    }
  }, [dateRange, selectedWeeklyTarget, selectedWeeklyTargets, weeklyTargets, customRange]);

  // Fetch agent-specific data
  const {
    data: agentInvoicesData,
    isLoading: isLoadingInvoices,
    error: invoicesError
  } = useGetAgentXeroInvoicesQuery({
    agentUsername,
    startDate: computedStartDate,
    endDate: computedEndDate
  }, {
    skip: !agentUsername || !computedStartDate || !computedEndDate
  });

  const {
    data: agentInBankData,
    isLoading: isLoadingInBank,
    error: inBankError
  } = useGetAgentXeroInBankQuery({
    agentUsername,
    startDate: computedStartDate,
    endDate: computedEndDate
  }, {
    skip: !agentUsername || !computedStartDate || !computedEndDate
  });

  // Fetch performance leaderboard
  const {
    data: leaderboardData,
    isLoading: isLoadingLeaderboard,
  } = useGetPerformanceLeaderboardQuery({
    startDate: computedStartDate,
    endDate: computedEndDate
  }, {
    skip: !computedStartDate || !computedEndDate
  });

  // Calculate KPI totals and targets
  const kpi1PersonalActual = agentInvoicesData?.reduce((sum, invoice) => sum + (invoice.gross || 0), 0) || 0;
  const kpi2PersonalActual = agentInBankData?.reduce((sum, payment) => sum + (payment.netAmount || 0), 0) || 0;

  // Get targets for current agent from selected weekly target
  const selectedTarget = weeklyTargets?.find(t => t.id.toString() === selectedWeeklyTarget);
  // The backend returns 'targets' array, not 'weeklyTargets'
  const agentTarget = selectedTarget?.targets?.find(target => target.profile?.username === agentUsername);
  const kpi1PersonalTarget = agentTarget?.kpi1Target || 0;
  const kpi2PersonalTarget = agentTarget?.kpi2Target || 0;

  // Calculate total targets (sum of all agents' targets)
  const kpi1TotalTarget = selectedTarget?.targets?.reduce((sum, target) => sum + (target.kpi1Target || 0), 0) || 0;
  const kpi2TotalTarget = selectedTarget?.targets?.reduce((sum, target) => sum + (target.kpi2Target || 0), 0) || 0;

  // Calculate total actuals (sum of all agents' actuals from leaderboard data)
  const kpi1TotalActual = leaderboardData?.kpi1Totals ?
    Object.values(leaderboardData.kpi1Totals).reduce((sum, actual) => sum + (actual || 0), 0) : 0;
  const kpi2TotalActual = leaderboardData?.kpi2Totals ?
    Object.values(leaderboardData.kpi2Totals).reduce((sum, actual) => sum + (actual || 0), 0) : 0;





  // Motivational quotes
  const motivationalQuotes = [
    "Success in sales comes from persistence and passion. Keep pushing forward!",
    "Every 'no' brings you closer to a 'yes'. Your next big win is just around the corner!",
    "Great salespeople are made, not born. You're building something amazing!",
    "Your dedication to excellence shows in every deal you close. Keep up the fantastic work!",
    "Sales is about solving problems and creating value. You're making a difference!",
    "The best time to plant a tree was 20 years ago. The second best time is now. Keep growing!",
    "Your targets are not just numbers - they're stepping stones to your success!"
  ];

  const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];

  // Filter functions for KPI tables
  const filteredKpi1Data = agentInvoicesData?.filter(invoice =>
    invoice.invoiceNumber?.toLowerCase().includes(kpi1SearchTerm.toLowerCase()) ||
    invoice.contactName?.toLowerCase().includes(kpi1SearchTerm.toLowerCase()) ||
    invoice.gross?.toString().includes(kpi1SearchTerm)
  ) || [];

  const filteredKpi2Data = agentInBankData?.filter(payment =>
    payment.invoiceNumber?.toLowerCase().includes(kpi2SearchTerm.toLowerCase()) ||
    payment.contactName?.toLowerCase().includes(kpi2SearchTerm.toLowerCase()) ||
    payment.netAmount?.toString().includes(kpi2SearchTerm)
  ) || [];

  if (!agentUsername) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500 text-3xl mb-4" />
          <p className="text-gray-600">Unable to load agent information. Please try logging in again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full py-6">
      {/* Enhanced Header Section */}
      <div className="flex flex-col mb-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
              <FontAwesomeIcon icon={faChartLine} className="mr-3 text-[#6E39CB]" />
              Monitoring Tool
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              Track your performance and stay motivated
            </p>
          </div>
        </div>

        {/* Enhanced Date Range Selection */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-50 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center gap-4">
            {/* Date Range Type Selector */}
            <div className="flex items-center gap-2">
              <label className="font-semibold text-sm text-gray-700">
                Time Period:
              </label>
              <select
                value={dateRange}
                onChange={(e) => {
                  setDateRange(e.target.value);
                  // Reset selections when changing type
                  setSelectedWeeklyTarget("");
                  setSelectedWeeklyTargets([]);
                  setCustomRange({ from: "", to: "" });
                }}
                className="bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              >
                <option value="weeklyTarget">Single Weekly Target</option>
                <option value="multipleTargets">Multiple Weekly Targets</option>
                <option value="thisMonth">This Month</option>
                <option value="thisYear">This Year</option>
                <option value="custom">Custom Date Range</option>
              </select>
            </div>

            {/* Conditional Selection Based on Type */}
            {dateRange === "weeklyTarget" && (
              <div className="flex items-center gap-2">
                <label className="font-medium text-sm text-gray-600">
                  Select Target:
                </label>
                <select
                  value={selectedWeeklyTarget}
                  onChange={(e) => setSelectedWeeklyTarget(e.target.value)}
                  className="bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] min-w-[200px]"
                  disabled={targetsLoading}
                >
                  {targetsLoading ? (
                    <option value="">Loading...</option>
                  ) : (
                    <>
                      <option value="">Select a target</option>
                      {weeklyTargets?.map((target) => {
                        const startDate = new Date(target.startDate);
                        const endDate = new Date(target.endDate);
                        const formattedStart = startDate.toLocaleDateString();
                        const formattedEnd = endDate.toLocaleDateString();
                        const now = new Date();
                        const isCurrent = now >= startDate && now <= endDate;

                        return (
                          <option key={target.id} value={target.id}>
                            {target.title} ({formattedStart} - {formattedEnd}) {isCurrent ? "• Current" : ""}
                          </option>
                        );
                      })}
                    </>
                  )}
                </select>
              </div>
            )}

            {dateRange === "multipleTargets" && (
              <div className="flex flex-col gap-3 min-w-[400px]">
                <div className="flex items-center justify-between">
                  <label className="font-medium text-sm text-gray-700">
                    Select Weekly Targets:
                  </label>
                  <div className="flex items-center gap-2">
                    <button
                      type="button"
                      onClick={() => setSelectedWeeklyTargets(weeklyTargets?.map(t => t.id.toString()) || [])}
                      className="text-xs text-[#6E39CB] hover:text-[#5B2FA3] font-medium"
                    >
                      Select All
                    </button>
                    <span className="text-gray-300">|</span>
                    <button
                      type="button"
                      onClick={() => setSelectedWeeklyTargets([])}
                      className="text-xs text-gray-500 hover:text-gray-700 font-medium"
                    >
                      Clear All
                    </button>
                  </div>
                </div>

                <div className="border border-gray-200 rounded-lg bg-white shadow-sm">
                  <div className="max-h-64 overflow-y-auto">
                    {targetsLoading ? (
                      <div className="p-4 text-center text-sm text-gray-500">
                        <div className="animate-pulse">Loading targets...</div>
                      </div>
                    ) : (
                      <div className="divide-y divide-gray-100">
                        {weeklyTargets?.map((target, index) => {
                          const startDate = new Date(target.startDate);
                          const endDate = new Date(target.endDate);
                          const formattedStart = startDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                          const formattedEnd = endDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                          const now = new Date();
                          const isCurrent = now >= startDate && now <= endDate;
                          const isSelected = selectedWeeklyTargets.includes(target.id.toString());

                          return (
                            <label
                              key={target.id}
                              className={`flex items-center p-3 cursor-pointer transition-all duration-200 ${
                                isSelected
                                  ? 'bg-[#6E39CB]/5 border-l-4 border-l-[#6E39CB]'
                                  : 'hover:bg-gray-50'
                              } ${index === 0 ? 'rounded-t-lg' : ''} ${index === weeklyTargets.length - 1 ? 'rounded-b-lg' : ''}`}
                            >
                              <div className="flex items-center space-x-3 w-full">
                                <input
                                  type="checkbox"
                                  value={target.id}
                                  checked={isSelected}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSelectedWeeklyTargets(prev => [...prev, target.id.toString()]);
                                    } else {
                                      setSelectedWeeklyTargets(prev => prev.filter(id => id !== target.id.toString()));
                                    }
                                  }}
                                  className="h-4 w-4 text-[#6E39CB] focus:ring-[#6E39CB] border-gray-300 rounded transition-colors"
                                />
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center justify-between">
                                    <span className={`font-medium text-sm ${isSelected ? 'text-[#6E39CB]' : 'text-gray-900'}`}>
                                      {target.title}
                                    </span>
                                    {isCurrent && (
                                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-[#6E39CB] text-white">
                                        Current
                                      </span>
                                    )}
                                  </div>
                                  <div className="text-xs text-gray-500 mt-1">
                                    {formattedStart} - {formattedEnd}
                                  </div>
                                </div>
                              </div>
                            </label>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>

                {selectedWeeklyTargets.length > 0 && (
                  <div className="flex items-center justify-between bg-[#6E39CB]/10 rounded-lg p-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-[#6E39CB] rounded-full"></div>
                      <span className="text-sm font-medium text-[#6E39CB]">
                        {selectedWeeklyTargets.length} target{selectedWeeklyTargets.length > 1 ? 's' : ''} selected
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={() => setSelectedWeeklyTargets([])}
                      className="text-xs text-gray-500 hover:text-gray-700"
                    >
                      Clear selection
                    </button>
                  </div>
                )}
              </div>
            )}

            {dateRange === "custom" && (
              <div className="flex items-center gap-2">
                <label className="font-medium text-sm text-gray-600">From:</label>
                <input
                  type="date"
                  value={customRange.from}
                  onChange={(e) => setCustomRange(prev => ({ ...prev, from: e.target.value }))}
                  className="bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                />
                <label className="font-medium text-sm text-gray-600">To:</label>
                <input
                  type="date"
                  value={customRange.to}
                  onChange={(e) => setCustomRange(prev => ({ ...prev, to: e.target.value }))}
                  className="bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                />
              </div>
            )}

            {(dateRange === "thisMonth" || dateRange === "thisYear") && (
              <div className="text-sm text-gray-600 font-medium">
                {dateRange === "thisMonth" ? "Showing data for current month" : "Showing data for current year"}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Loading State */}
      {(targetsLoading || isLoadingInvoices || isLoadingInBank) && (
        <div className="flex items-center justify-center h-64">
          <FontAwesomeIcon icon={faSpinner} className="text-[#6E39CB] text-2xl animate-spin mr-3" />
          <span className="text-gray-500">Loading your performance data...</span>
        </div>
      )}

      {/* Error State */}
      {(targetsError || invoicesError || inBankError) && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex">
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-400 mr-3 mt-1" />
            <div>
              <h3 className="text-sm font-medium text-red-800">Error Loading Data</h3>
              <p className="text-sm text-red-700 mt-1">
                There was an issue loading your performance data. Please try refreshing the page.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      {!targetsLoading && !isLoadingInvoices && !isLoadingInBank && (
        (dateRange === "weeklyTarget" && selectedWeeklyTarget) ||
        (dateRange === "multipleTargets" && selectedWeeklyTargets.length > 0) ||
        (dateRange === "thisMonth") ||
        (dateRange === "thisYear") ||
        (dateRange === "custom" && customRange.from && customRange.to)
      ) && (
        <>
          {/* KPI Performance Overview - Redesigned with Better Hierarchy and Grouping */}
          <div className="space-y-8 mb-8">
            {/* KPI1: Team Invoice Section */}
            <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-6 border border-purple-100">
              <div className="flex items-center mb-6">
                <div className="p-3 bg-purple-100 rounded-lg mr-4">
                  <FontAwesomeIcon icon={faFileInvoiceDollar} className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">KPI1: Team Invoice</h3>
                  <p className="text-sm text-gray-600">Revenue generation performance metrics</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Team Target */}
                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500 mb-1">Team Target</p>
                      <p className="text-3xl font-bold text-gray-900">${kpi1TotalTarget.toLocaleString()}</p>
                    </div>
                    <div className="p-2 bg-gray-100 rounded-lg">
                      <FontAwesomeIcon icon={faFileInvoiceDollar} className="h-5 w-5 text-gray-600" />
                    </div>
                  </div>
                  <div className="text-xs text-gray-500">
                    Total team revenue goal
                  </div>
                </div>

                {/* Team Actual */}
                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500 mb-1">Team Actual</p>
                      <p className={`text-3xl font-bold ${kpi1TotalActual >= kpi1TotalTarget ? 'text-green-600' : 'text-gray-900'}`}>
                        ${kpi1TotalActual.toLocaleString()}
                      </p>
                    </div>
                    <div className={`p-2 rounded-lg ${kpi1TotalActual >= kpi1TotalTarget ? 'bg-green-100' : 'bg-orange-100'}`}>
                      <FontAwesomeIcon icon={faFileInvoiceDollar} className={`h-5 w-5 ${kpi1TotalActual >= kpi1TotalTarget ? 'text-green-600' : 'text-orange-600'}`} />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className={`text-sm font-medium ${kpi1TotalActual >= kpi1TotalTarget ? 'text-green-600' : 'text-orange-600'}`}>
                      {kpi1TotalTarget > 0 ? Math.round((kpi1TotalActual / kpi1TotalTarget) * 100) : 0}% of Target
                    </div>
                    {/* Progress Bar */}
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          kpi1TotalActual >= kpi1TotalTarget ? 'bg-green-500' :
                          (kpi1TotalActual / kpi1TotalTarget) >= 0.75 ? 'bg-blue-500' :
                          (kpi1TotalActual / kpi1TotalTarget) >= 0.5 ? 'bg-yellow-500' : 'bg-orange-500'
                        }`}
                        style={{ width: `${Math.min(100, kpi1TotalTarget > 0 ? (kpi1TotalActual / kpi1TotalTarget) * 100 : 0)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>

                {/* Personal Performance */}
                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500 mb-1">Your Performance</p>
                      <p className={`text-3xl font-bold ${kpi1PersonalActual >= kpi1PersonalTarget ? 'text-green-600' : 'text-gray-900'}`}>
                        ${kpi1PersonalActual.toLocaleString()}
                      </p>
                    </div>
                    <div className={`p-2 rounded-lg ${kpi1PersonalActual >= kpi1PersonalTarget ? 'bg-green-100' : 'bg-orange-100'}`}>
                      <FontAwesomeIcon icon={faFileInvoiceDollar} className={`h-5 w-5 ${kpi1PersonalActual >= kpi1PersonalTarget ? 'text-green-600' : 'text-orange-600'}`} />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className={`text-sm font-medium ${kpi1PersonalActual >= kpi1PersonalTarget ? 'text-green-600' : 'text-orange-600'}`}>
                      {kpi1PersonalTarget > 0 ? Math.round((kpi1PersonalActual / kpi1PersonalTarget) * 100) : 0}% of Personal Target
                    </div>
                    {/* Progress Bar */}
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          kpi1PersonalActual >= kpi1PersonalTarget ? 'bg-green-500' :
                          (kpi1PersonalActual / kpi1PersonalTarget) >= 0.75 ? 'bg-blue-500' :
                          (kpi1PersonalActual / kpi1PersonalTarget) >= 0.5 ? 'bg-yellow-500' : 'bg-orange-500'
                        }`}
                        style={{ width: `${Math.min(100, kpi1PersonalTarget > 0 ? (kpi1PersonalActual / kpi1PersonalTarget) * 100 : 0)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* KPI2: Team Money Section */}
            <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 border border-blue-100">
              <div className="flex items-center mb-6">
                <div className="p-3 bg-blue-100 rounded-lg mr-4">
                  <FontAwesomeIcon icon={faMoneyBillWave} className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">KPI2: Team Money</h3>
                  <p className="text-sm text-gray-600">Money in bank performance metrics</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Team Target */}
                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500 mb-1">Team Target</p>
                      <p className="text-3xl font-bold text-gray-900">${kpi2TotalTarget.toLocaleString()}</p>
                    </div>
                    <div className="p-2 bg-gray-100 rounded-lg">
                      <FontAwesomeIcon icon={faMoneyBillWave} className="h-5 w-5 text-gray-600" />
                    </div>
                  </div>
                  <div className="text-xs text-gray-500">
                    Total team money goal
                  </div>
                </div>

                {/* Team Actual */}
                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500 mb-1">Team Actual</p>
                      <p className={`text-3xl font-bold ${kpi2TotalActual >= kpi2TotalTarget ? 'text-green-600' : 'text-gray-900'}`}>
                        ${kpi2TotalActual.toLocaleString()}
                      </p>
                    </div>
                    <div className={`p-2 rounded-lg ${kpi2TotalActual >= kpi2TotalTarget ? 'bg-green-100' : 'bg-orange-100'}`}>
                      <FontAwesomeIcon icon={faMoneyBillWave} className={`h-5 w-5 ${kpi2TotalActual >= kpi2TotalTarget ? 'text-green-600' : 'text-orange-600'}`} />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className={`text-sm font-medium ${kpi2TotalActual >= kpi2TotalTarget ? 'text-green-600' : 'text-orange-600'}`}>
                      {kpi2TotalTarget > 0 ? Math.round((kpi2TotalActual / kpi2TotalTarget) * 100) : 0}% of Target
                    </div>
                    {/* Progress Bar */}
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          kpi2TotalActual >= kpi2TotalTarget ? 'bg-green-500' :
                          (kpi2TotalActual / kpi2TotalTarget) >= 0.75 ? 'bg-blue-500' :
                          (kpi2TotalActual / kpi2TotalTarget) >= 0.5 ? 'bg-yellow-500' : 'bg-orange-500'
                        }`}
                        style={{ width: `${Math.min(100, kpi2TotalTarget > 0 ? (kpi2TotalActual / kpi2TotalTarget) * 100 : 0)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>

                {/* Personal Performance */}
                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500 mb-1">Your Performance</p>
                      <p className={`text-3xl font-bold ${kpi2PersonalActual >= kpi2PersonalTarget ? 'text-green-600' : 'text-gray-900'}`}>
                        ${kpi2PersonalActual.toLocaleString()}
                      </p>
                    </div>
                    <div className={`p-2 rounded-lg ${kpi2PersonalActual >= kpi2PersonalTarget ? 'bg-green-100' : 'bg-orange-100'}`}>
                      <FontAwesomeIcon icon={faMoneyBillWave} className={`h-5 w-5 ${kpi2PersonalActual >= kpi2PersonalTarget ? 'text-green-600' : 'text-orange-600'}`} />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className={`text-sm font-medium ${kpi2PersonalActual >= kpi2PersonalTarget ? 'text-green-600' : 'text-orange-600'}`}>
                      {kpi2PersonalTarget > 0 ? Math.round((kpi2PersonalActual / kpi2PersonalTarget) * 100) : 0}% of Personal Target
                    </div>
                    {/* Progress Bar */}
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          kpi2PersonalActual >= kpi2PersonalTarget ? 'bg-green-500' :
                          (kpi2PersonalActual / kpi2PersonalTarget) >= 0.75 ? 'bg-blue-500' :
                          (kpi2PersonalActual / kpi2PersonalTarget) >= 0.5 ? 'bg-yellow-500' : 'bg-orange-500'
                        }`}
                        style={{ width: `${Math.min(100, kpi2PersonalTarget > 0 ? (kpi2PersonalActual / kpi2PersonalTarget) * 100 : 0)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* KPI1 LEADERBOARD - FULL WIDTH */}
          <div className="mb-6">
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50">
              <div className="flex justify-between items-center mb-4">
                <div>
                  <h5 className="text-md font-medium text-gray-900">KPI1 Performance Leaderboard</h5>
                  <p className="text-xs text-gray-500 mt-1">
                    Revenue Generation - Ranked by achievement percentage
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <FontAwesomeIcon icon={faCheckCircle} className="mr-1 h-3 w-3" /> Target Achieved
                  </span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    <FontAwesomeIcon icon={faArrowUp} className="mr-1 h-3 w-3" /> Target Pending
                  </span>
                </div>
              </div>

              {isLoadingLeaderboard ? (
                <div className="flex items-center justify-center h-32">
                  <FontAwesomeIcon icon={faSpinner} className="text-[#6E39CB] text-xl animate-spin mr-3" />
                  <span className="text-gray-500">Loading leaderboard...</span>
                </div>
              ) : leaderboardData ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-[#F4F5F9]">
                      <tr>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                          Rank
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Agent
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Target
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actual
                        </th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Progress
                        </th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {Object.entries(leaderboardData.kpi1Totals || {})
                        .map(([agent, actual]) => {
                          // Find the target for this agent from the selected weekly target
                          const agentTargetData = selectedTarget?.targets?.find(target => target.profile?.username === agent);
                          const target = agentTargetData?.kpi1Target || 0;
                          const agentName = agentTargetData?.profile?.fullName || agent;
                          return {
                            username: agent,
                            name: agentName,
                            actual: actual,
                            target: target,
                            percentage: target > 0 ? Math.round((actual / target) * 100) : 0,
                            status: actual >= target ? "achieved" : "pending"
                          };
                        })
                        .sort((a, b) => {
                          const aPercentage = a.target > 0 ? (a.actual / a.target) : 0;
                          const bPercentage = b.target > 0 ? (b.actual / b.target) : 0;
                          return bPercentage - aPercentage;
                        })
                        .map((agent, idx) => {
                          const kpi1Met = agent.status === "achieved";
                          const isCurrentUser = agent.username === agentUsername;

                          // Determine row styling based on rank and achievement
                          let rowClass = "transition-colors";
                          let rankClass = "";

                          if (isCurrentUser) {
                            rowClass += " bg-purple-50 hover:bg-purple-100 border-l-4 border-purple-500";
                          } else if (kpi1Met) {
                            rowClass += " bg-green-50 hover:bg-green-100";
                          } else if (idx >= Object.keys(leaderboardData.kpi1Totals || {}).length - 3) {
                            // Bottom 3 performers who didn't meet target
                            rowClass += " bg-gray-50 hover:bg-gray-100";
                          } else {
                            rowClass += " hover:bg-gray-50";
                          }

                          // Special styling for top 3
                          if (idx === 0) {
                            rankClass = "bg-yellow-100 text-yellow-800 border border-yellow-300";
                          } else if (idx === 1) {
                            rankClass = "bg-gray-200 text-gray-800 border border-gray-300";
                          } else if (idx === 2) {
                            rankClass = "bg-orange-100 text-orange-800 border border-orange-300";
                          } else {
                            rankClass = "bg-gray-100 text-gray-600";
                          }

                          return (
                            <tr key={idx} className={rowClass}>
                              <td className="px-4 py-4 whitespace-nowrap text-center">
                                <div className="flex justify-center">
                                  <span className={`h-8 w-8 rounded-full flex items-center justify-center text-sm font-bold ${rankClass}`}>
                                    {idx + 1}
                                  </span>
                                </div>
                              </td>
                              <td className="px-4 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mr-3 flex-shrink-0">
                                    <span className="text-[#6E39CB] font-medium">
                                      {agent.name.charAt(0).toUpperCase()}
                                    </span>
                                  </div>
                                  <span className={`font-medium text-sm ${isCurrentUser ? 'text-purple-700 font-bold' : ''}`}>
                                    {isCurrentUser ? 'You' : agent.name}
                                  </span>
                                </div>
                              </td>
                              <td className="px-4 py-4 whitespace-nowrap text-right text-sm">
                                ${agent.target.toLocaleString()}
                              </td>
                              <td className="px-4 py-4 whitespace-nowrap text-right text-sm">
                                <span className={kpi1Met ? 'font-medium text-green-600' : 'font-normal'}>
                                  ${agent.actual.toLocaleString()}
                                </span>
                              </td>
                              <td className="px-4 py-4 whitespace-nowrap">
                                <div className="flex items-center justify-center">
                                  <div className="w-full max-w-xs bg-gray-200 rounded-full h-2.5">
                                    <div
                                      className={`h-2.5 rounded-full ${kpi1Met ? 'bg-green-500' : agent.percentage >= 75 ? 'bg-blue-500' : agent.percentage >= 50 ? 'bg-yellow-500' : 'bg-orange-500'}`}
                                      style={{ width: `${Math.min(100, agent.percentage)}%` }}
                                    ></div>
                                  </div>
                                  <span className={`ml-2 text-xs font-medium ${kpi1Met ? 'text-green-600' : agent.percentage >= 75 ? 'text-blue-600' : agent.percentage >= 50 ? 'text-yellow-600' : 'text-orange-500'}`}>
                                    {agent.percentage}%
                                  </span>
                                </div>
                              </td>
                              <td className="px-4 py-4 whitespace-nowrap text-center">
                                {kpi1Met ? (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <FontAwesomeIcon icon={faCheckCircle} className="mr-1" /> Achieved
                                  </span>
                                ) : (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <FontAwesomeIcon icon={faArrowUp} className="mr-1" /> {100 - agent.percentage}% to go
                                  </span>
                                )}
                              </td>
                            </tr>
                          );
                        })}

                      {Object.keys(leaderboardData.kpi1Totals || {}).length === 0 && (
                        <tr>
                          <td colSpan="6" className="px-6 py-4 text-center text-sm text-gray-500">
                            No data available
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <FontAwesomeIcon icon={faTrophy} className="text-gray-400 text-3xl mb-4" />
                  <p className="text-gray-500">Leaderboard data will be available once loaded</p>
                </div>
              )}
            </div>
          </div>

          {/* KPI2 LEADERBOARD - FULL WIDTH */}
          <div className="mb-6">
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50">
              <div className="flex justify-between items-center mb-4">
                <div>
                  <h5 className="text-md font-medium text-gray-900">KPI2 Performance Leaderboard</h5>
                  <p className="text-xs text-gray-500 mt-1">
                    Money in Bank - Ranked by achievement percentage
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <FontAwesomeIcon icon={faCheckCircle} className="mr-1 h-3 w-3" /> Target Achieved
                  </span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    <FontAwesomeIcon icon={faArrowUp} className="mr-1 h-3 w-3" /> Target Pending
                  </span>
                </div>
              </div>

              {isLoadingLeaderboard ? (
                <div className="flex items-center justify-center h-32">
                  <FontAwesomeIcon icon={faSpinner} className="text-[#6E39CB] text-xl animate-spin mr-3" />
                  <span className="text-gray-500">Loading leaderboard...</span>
                </div>
              ) : leaderboardData ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-[#F4F5F9]">
                      <tr>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                          Rank
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Agent
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Target
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actual
                        </th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Progress
                        </th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {Object.entries(leaderboardData.kpi2Totals || {})
                        .map(([agent, actual]) => {
                          // Find the target for this agent from the selected weekly target
                          const agentTargetData = selectedTarget?.targets?.find(target => target.profile?.username === agent);
                          const target = agentTargetData?.kpi2Target || 0;
                          const agentName = agentTargetData?.profile?.fullName || agent;
                          return {
                            username: agent,
                            name: agentName,
                            actual: actual,
                            target: target,
                            percentage: target > 0 ? Math.round((actual / target) * 100) : 0,
                            status: actual >= target ? "achieved" : "pending"
                          };
                        })
                        .sort((a, b) => {
                          const aPercentage = a.target > 0 ? (a.actual / a.target) : 0;
                          const bPercentage = b.target > 0 ? (b.actual / b.target) : 0;
                          return bPercentage - aPercentage;
                        })
                        .map((agent, idx) => {
                          const kpi2Met = agent.status === "achieved";
                          const isCurrentUser = agent.username === agentUsername;

                          // Determine row styling based on rank and achievement
                          let rowClass = "transition-colors";
                          let rankClass = "";

                          if (isCurrentUser) {
                            rowClass += " bg-blue-50 hover:bg-blue-100 border-l-4 border-blue-500";
                          } else if (kpi2Met) {
                            rowClass += " bg-green-50 hover:bg-green-100";
                          } else if (idx >= Object.keys(leaderboardData.kpi2Totals || {}).length - 3) {
                            // Bottom 3 performers who didn't meet target
                            rowClass += " bg-gray-50 hover:bg-gray-100";
                          } else {
                            rowClass += " hover:bg-gray-50";
                          }

                          // Special styling for top 3
                          if (idx === 0) {
                            rankClass = "bg-yellow-100 text-yellow-800 border border-yellow-300";
                          } else if (idx === 1) {
                            rankClass = "bg-gray-200 text-gray-800 border border-gray-300";
                          } else if (idx === 2) {
                            rankClass = "bg-orange-100 text-orange-800 border border-orange-300";
                          } else {
                            rankClass = "bg-gray-100 text-gray-600";
                          }

                          return (
                            <tr key={idx} className={rowClass}>
                              <td className="px-4 py-4 whitespace-nowrap text-center">
                                <div className="flex justify-center">
                                  <span className={`h-8 w-8 rounded-full flex items-center justify-center text-sm font-bold ${rankClass}`}>
                                    {idx + 1}
                                  </span>
                                </div>
                              </td>
                              <td className="px-4 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mr-3 flex-shrink-0">
                                    <span className="text-[#6E39CB] font-medium">
                                      {agent.name.charAt(0).toUpperCase()}
                                    </span>
                                  </div>
                                  <span className={`font-medium text-sm ${isCurrentUser ? 'text-blue-700 font-bold' : ''}`}>
                                    {isCurrentUser ? 'You' : agent.name}
                                  </span>
                                </div>
                              </td>
                              <td className="px-4 py-4 whitespace-nowrap text-right text-sm">
                                ${agent.target.toLocaleString()}
                              </td>
                              <td className="px-4 py-4 whitespace-nowrap text-right text-sm">
                                <span className={kpi2Met ? 'font-medium text-green-600' : 'font-normal'}>
                                  ${agent.actual.toLocaleString()}
                                </span>
                              </td>
                              <td className="px-4 py-4 whitespace-nowrap">
                                <div className="flex items-center justify-center">
                                  <div className="w-full max-w-xs bg-gray-200 rounded-full h-2.5">
                                    <div
                                      className={`h-2.5 rounded-full ${kpi2Met ? 'bg-green-500' : agent.percentage >= 75 ? 'bg-blue-500' : agent.percentage >= 50 ? 'bg-yellow-500' : 'bg-orange-500'}`}
                                      style={{ width: `${Math.min(100, agent.percentage)}%` }}
                                    ></div>
                                  </div>
                                  <span className={`ml-2 text-xs font-medium ${kpi2Met ? 'text-green-600' : agent.percentage >= 75 ? 'text-blue-600' : agent.percentage >= 50 ? 'text-yellow-600' : 'text-orange-500'}`}>
                                    {agent.percentage}%
                                  </span>
                                </div>
                              </td>
                              <td className="px-4 py-4 whitespace-nowrap text-center">
                                {kpi2Met ? (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <FontAwesomeIcon icon={faCheckCircle} className="mr-1" /> Achieved
                                  </span>
                                ) : (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <FontAwesomeIcon icon={faArrowUp} className="mr-1" /> {100 - agent.percentage}% to go
                                  </span>
                                )}
                              </td>
                            </tr>
                          );
                        })}

                      {Object.keys(leaderboardData.kpi2Totals || {}).length === 0 && (
                        <tr>
                          <td colSpan="6" className="px-6 py-4 text-center text-sm text-gray-500">
                            No data available
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <FontAwesomeIcon icon={faTrophy} className="text-gray-400 text-3xl mb-4" />
                  <p className="text-gray-500">Leaderboard data will be available once loaded</p>
                </div>
              )}
            </div>
          </div>

          {/* KPI Tables */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* KPI1 Table */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-50">
              <div className="px-6 py-4 border-b border-gray-100">
                <div className="flex justify-between items-center mb-2">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">KPI1 - Personal Invoice Data</h3>
                    <p className="text-sm text-gray-500">Your personal invoicing transactions for this period</p>
                  </div>
                  <div className="text-sm text-gray-500">
                    {filteredKpi1Data.length} of {agentInvoicesData?.length || 0} invoices
                  </div>
                </div>
                {/* Search Input */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FontAwesomeIcon icon={faSearch} className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search invoices..."
                    value={kpi1SearchTerm}
                    onChange={(e) => setKpi1SearchTerm(e.target.value)}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
                  />
                </div>
              </div>
              <div className="overflow-x-auto max-h-96 overflow-y-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50 sticky top-0">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Invoice #
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Contact
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredKpi1Data.length > 0 ? (
                      filteredKpi1Data.map((invoice, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {invoice.invoiceNumber}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {invoice.contactName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${invoice.gross?.toLocaleString() || '0'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(invoice.invoiceDate).toLocaleDateString('en-GB', {
                              day: '2-digit',
                              month: 'short',
                              year: 'numeric'
                            })}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="4" className="px-6 py-4 text-center text-sm text-gray-500">
                          {kpi1SearchTerm ? 'No invoices match your search' : 'No invoice data for this period'}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* KPI2 Table */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-50">
              <div className="px-6 py-4 border-b border-gray-100">
                <div className="flex justify-between items-center mb-2">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">KPI2 - Personal Payment Data</h3>
                    <p className="text-sm text-gray-500">Your personal payment collections for this period</p>
                  </div>
                  <div className="text-sm text-gray-500">
                    {filteredKpi2Data.length} of {agentInBankData?.length || 0} payments
                  </div>
                </div>
                {/* Search Input */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FontAwesomeIcon icon={faSearch} className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search payments..."
                    value={kpi2SearchTerm}
                    onChange={(e) => setKpi2SearchTerm(e.target.value)}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>
              <div className="overflow-x-auto max-h-96 overflow-y-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50 sticky top-0">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reference
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Contact
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredKpi2Data.length > 0 ? (
                      filteredKpi2Data.map((payment, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {payment.invoiceNumber}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {payment.contactName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${payment.netAmount?.toLocaleString() || '0'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(payment.insertDate).toLocaleDateString('en-GB', {
                              day: '2-digit',
                              month: 'short',
                              year: 'numeric'
                            })}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="4" className="px-6 py-4 text-center text-sm text-gray-500">
                          {kpi2SearchTerm ? 'No payments match your search' : 'No payment data for this period'}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Motivational Quote */}
          <div className="bg-gradient-to-r from-[#6E39CB] to-blue-600 rounded-lg p-6 mb-8 text-white">
            <div className="flex items-start">
              <div className="text-2xl opacity-50 mr-4 mt-1">💡</div>
              <div>
                <p className="text-lg font-medium mb-2">{randomQuote}</p>
                <p className="text-sm opacity-75">Keep pushing towards your goals! 🚀</p>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default AgentMonitoringTool;
