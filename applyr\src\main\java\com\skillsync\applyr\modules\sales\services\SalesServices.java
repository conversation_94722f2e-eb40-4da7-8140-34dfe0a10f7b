package com.skillsync.applyr.modules.sales.services;


import com.skillsync.applyr.core.models.entities.Qualification;
import com.skillsync.applyr.core.models.entities.TroubleAnswer;
import com.skillsync.applyr.core.models.entities.Troubles;
import com.skillsync.applyr.core.models.enums.TroubleStatus;
import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.modules.company.repositories.QualificationRepository;
import com.skillsync.applyr.modules.company.repositories.SalesAgentRepository;
import com.skillsync.applyr.modules.company.services.CompanyServices;
import com.skillsync.applyr.modules.sales.models.CreateTroubleDTO;
import com.skillsync.applyr.modules.sales.models.TroubleAnswerDTO;
import com.skillsync.applyr.modules.sales.models.TroubleAnswerReqDTO;
import com.skillsync.applyr.modules.sales.models.TroublesResponseDTO;
import com.skillsync.applyr.modules.sales.repositories.TroubleRepository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SalesServices {

    private final QualificationRepository qualificationRepository;
    private final TroubleRepository troubleRepository;
    private final CompanyServices companyServices;


    public SalesServices(QualificationRepository qualificationRepository, TroubleRepository troubleRepository, SalesAgentRepository salesAgentRepository, CompanyServices companyServices) {
        this.qualificationRepository = qualificationRepository;
        this.troubleRepository = troubleRepository;
        this.companyServices = companyServices;
    }

    public List<Qualification> getQualifications() {
        return qualificationRepository.findAll();
    }

    public SuccessResponse addTrouble(CreateTroubleDTO troubleDTO) {
        Troubles raw = new Troubles(troubleDTO);
        troubleRepository.save(raw);
        return new SuccessResponse("Your trouble has been successfully added");
    }
    
    public List<TroublesResponseDTO> getAllTroubles() {
        return fromTroublesToTroublesDTO(troubleRepository.findAll());
    }

    private List<TroublesResponseDTO> fromTroublesToTroublesDTO(List<Troubles> all) {
        List<TroublesResponseDTO> responseDTOS = new ArrayList<>();
        for(Troubles raw : all) {
            TroublesResponseDTO dto = new TroublesResponseDTO();

            dto.setTroubleId(raw.getId());
            dto.setCreatedBy(companyServices.findProfileFromUsername(raw.getCreatedBy()));
            dto.setAssignedTo(companyServices.findProfileFromUsername(raw.getAllocatedTo()));

            dto.setQuestions(raw.getQuestion());
            dto.setDueDate(raw.getDueDate());
            dto.setStatus(raw.getStatus());
            dto.setCreatedDate(raw.getCreatedDate());

            for (TroubleAnswer rawAnswer : raw.getAnswers()) {
                TroubleAnswerDTO answerDTO = new TroubleAnswerDTO();
                answerDTO.setAnswer(rawAnswer.getAnswer());

                answerDTO.setAnsweredBy(companyServices.findProfileFromUsername(rawAnswer.getCreatedBy()));
                answerDTO.setAnsweredAt(rawAnswer.getCreatedDate());

                dto.addAnswer(answerDTO);
            }
            responseDTOS.add(dto);
        }
        return responseDTOS;
    }

    public SuccessResponse addComments(long id, TroubleAnswerReqDTO answer) {
        Troubles troubles = troubleRepository.getReferenceById(id);
        troubles.addAnswer(answer);
        troubleRepository.save(troubles);
        return new SuccessResponse("Successfully added comments to trouble!");
    }

    public SuccessResponse changeStatusOfTrouble(long id, TroubleStatus status) {
        Troubles troubles = troubleRepository.getReferenceById(id);
        troubles.setStatus(status);
        troubleRepository.save(troubles);
        return new SuccessResponse("Successfully changed status of trouble!");
    }

    public SuccessResponse deleteTrouble(long id) {
        troubleRepository.deleteById(id);
        return new SuccessResponse("Successfully deleted trouble.");
    }

    public SuccessResponse editTrouble(long id, CreateTroubleDTO reqDTO) {

        Troubles troubles = troubleRepository.getReferenceById(id);
        troubles.setQuestion(reqDTO.getQuestion());
        troubles.setDueDate(reqDTO.getDueDate());
        troubles.setAllocatedTo(reqDTO.getAssignedTo());
        troubleRepository.save(troubles);
        return new SuccessResponse("Successfully updated trouble!");
    }

}
